{"name": "vibe-coding-dev", "version": "1.0.0", "description": "Web IDE platform with AI-assisted coding capabilities", "private": true, "type": "module", "scripts": {"dev": "concurrently \"pnpm run server:dev\" \"pnpm run web:dev\"", "dev:server": "cd server && bun run dev", "dev:web": "cd web && pnpm run dev", "server:dev": "cd server && bun run dev", "server:build": "cd server && bun run build", "server:start": "cd server && bun run server", "web:dev": "cd web && pnpm run dev", "web:build": "cd web && pnpm run build", "web:start": "cd web && pnpm run start", "build": "pnpm run server:build && pnpm run web:build", "install:all": "pnpm install && cd server && bun install && cd ../web && pnpm install", "clean": "rm -rf node_modules && cd server && rm -rf node_modules && cd ../web && rm -rf node_modules", "setup": "pnpm run install:all && cd server && bun run setup"}, "devDependencies": {"concurrently": "^9.1.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "packageManager": "pnpm@9.0.0"}